---
alwaysApply: true
---

# 基础说明

## 系统环境

- **操作系统**: Windows 11 (Build 26100)
- **命令行工具**: PowerShell 7

### 版本控制

- **Git**: 最新版本
- **提交规范**: 使用 yarn git:push 命令
- **git 本地操作-MCP 工具**: git-mcp-server

### Node.js 环境

- **Node.js**: 20.19.3
- **包管理器**: yarn@1.22.22
- **配置文件**: package.json

## 项目规则文件

- **MCP 规则.md** - MCP 工具使用规范 (P0 级)
- **角色设定.md** - AI 智能编程伙伴角色定位 (P0 级)
- **环境说明.md** - 本文件，环境配置记录
- **代码规范.md** - Vue3 项目开发规范

## 注意事项

- 除非用户明确要求，否则不做 git 相关操作
- Git 操作强制使用 git-mcp-server 工具（推送代码除外）
- 复杂问题优先使用 sequential-thinking 分析
- 当你读取规则文件时，请按照如下格式提示我,用几个显示几个（必须！！！）
  规则使用中：
  [📄 xxx][📄 xxx] [📄 xxx]
- 回答前，请先在首行输出当前模型信息(模型名称、规模、类型、更新日期)
- 所有回复和思考都使用中文
- 回复时适当使用表情符号，每行最多 2 个表情
- 当回复涉及架构、流程、数据关系、时间线、复杂逻辑，或明确要求流程图时，使用彩色 Mermaid 回复。
